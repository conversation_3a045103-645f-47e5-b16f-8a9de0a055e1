/**
 * Firebase Cloud Functions for Flashcard Application
 *
 * This module contains scheduled functions for fetching and caching
 * exchange rates to improve performance and reduce API costs.
 */

import {onSchedule} from "firebase-functions/v2/scheduler";
import {onRequest} from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";
import {initializeApp} from "firebase-admin/app";
import {getFirestore} from "firebase-admin/firestore";

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// Exchange rate configuration
const EXCHANGE_RATE_API_URL = "https://v6.exchangerate-api.com/v6/************************/latest/USD";
const FALLBACK_ZAR_TO_USD_RATE = 0.0558; // Updated June 2025

// Interface for API response structure
interface ExchangeRateAPIResponse {
  result: string;
  base_code: string;
  time_last_update_unix: number;
  time_next_update_unix: number;
  conversion_rates: {
    ZAR: number;
    [key: string]: number;
  };
}

// Interface for exchange rate data
interface ExchangeRateData {
  zarToUsdRate: number;
  usdToZarRate: number;
  lastUpdated: FirebaseFirestore.Timestamp;
  source: "api" | "fallback";
  apiResponse?: {
    result: string;
    baseCode: string;
    timeLastUpdate: number;
    timeNextUpdate: number;
  };
  error?: string;
}

/**
 * Scheduled function that runs daily at 00:00 UTC to fetch exchange rates
 * and store them in Firestore for client-side consumption
 */
export const fetchDailyExchangeRates = onSchedule({
  schedule: "0 0 * * *", // Daily at 00:00 UTC
  timeZone: "UTC",
  memory: "256MiB",
  timeoutSeconds: 60,
}, async (event) => {
  logger.info("Starting daily exchange rate fetch", {
    scheduledTime: event.scheduleTime,
    jobName: event.jobName,
  });

  try {
    const exchangeRateData = await fetchExchangeRateFromAPI();
    await storeExchangeRateInFirestore(exchangeRateData);

    logger.info("Successfully updated exchange rates", {
      rate: exchangeRateData.zarToUsdRate,
      source: exchangeRateData.source,
    });
  } catch (error) {
    logger.error("Failed to update exchange rates", {
      error: error instanceof Error ? error.message : String(error),
    });

    // Store fallback rate if API completely fails
    try {
      const fallbackData: ExchangeRateData = {
        zarToUsdRate: FALLBACK_ZAR_TO_USD_RATE,
        usdToZarRate: 1 / FALLBACK_ZAR_TO_USD_RATE,
        lastUpdated: FirebaseFirestore.Timestamp.now(),
        source: "fallback",
        error: error instanceof Error ? error.message : String(error),
      };

      await storeExchangeRateInFirestore(fallbackData);
      logger.warn("Stored fallback exchange rate due to API failure");
    } catch (fallbackError) {
      const errorMessage = fallbackError instanceof Error ?
        fallbackError.message : String(fallbackError);
      logger.error("Failed to store fallback exchange rate", {
        error: errorMessage,
      });
    }
  }
});

/**
 * Fetches exchange rate from ExchangeRate-API v6
 * Includes retry logic and comprehensive error handling
 */
async function fetchExchangeRateFromAPI(): Promise<ExchangeRateData> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`Fetching exchange rate (attempt ${attempt}/${maxRetries})`);

      const response = await fetch(EXCHANGE_RATE_API_URL, {
        method: "GET",
        headers: {
          "Accept": "application/json",
          "User-Agent": "Flashcard-App/1.0",
        },
        // 10 second timeout
        signal: AbortSignal.timeout(10000),
      });

      if (!response.ok) {
        const statusText = response.statusText;
        throw new Error(
          `API responded with status: ${response.status} ${statusText}`
        );
      }

      const data: ExchangeRateAPIResponse = await response.json();

      // Validate response structure
      const hasValidRates = data.conversion_rates &&
        typeof data.conversion_rates.ZAR === "number";
      if (!hasValidRates) {
        throw new Error("Invalid response format from exchange rate API");
      }

      if (data.result !== "success") {
        throw new Error(`API returned error result: ${data.result}`);
      }

      // Calculate rates
      const usdToZarRate = data.conversion_rates.ZAR;
      const zarToUsdRate = 1 / usdToZarRate;

      // Validate rates are reasonable (basic sanity check)
      if (zarToUsdRate < 0.01 || zarToUsdRate > 1) {
        throw new Error(`Exchange rate seems unreasonable: ${zarToUsdRate}`);
      }

      const exchangeRateData: ExchangeRateData = {
        zarToUsdRate,
        usdToZarRate,
        lastUpdated: FirebaseFirestore.Timestamp.now(),
        source: "api",
        apiResponse: {
          result: data.result,
          baseCode: data.base_code,
          timeLastUpdate: data.time_last_update_unix,
          timeNextUpdate: data.time_next_update_unix,
        },
      };

      logger.info("Successfully fetched exchange rate from API", {
        zarToUsdRate,
        usdToZarRate,
        baseCode: data.base_code,
      });

      return exchangeRateData;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      logger.warn(`Exchange rate fetch attempt ${attempt} failed`, {
        error: lastError.message,
        willRetry: attempt < maxRetries,
      });

      // Wait before retry (exponential backoff)
      if (attempt < maxRetries) {
        const delayMs = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
  }

  // If all retries failed, throw the last error
  const errorMessage = "Failed to fetch exchange rate after all retries";
  throw lastError || new Error(errorMessage);
}

/**
 * Stores exchange rate data in Firestore
 * Uses a single document for the current exchange rate
 * @param {ExchangeRateData} data - The exchange rate data to store
 */
async function storeExchangeRateInFirestore(
  data: ExchangeRateData
): Promise<void> {
  try {
    const docRef = db.collection("exchangeRates").doc("current");

    await docRef.set(data, {merge: false});

    logger.info("Successfully stored exchange rate in Firestore", {
      zarToUsdRate: data.zarToUsdRate,
      source: data.source,
      docPath: docRef.path,
    });
  } catch (error) {
    logger.error("Failed to store exchange rate in Firestore", {
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

/**
 * Manual trigger function for testing exchange rate fetching
 * Can be called via HTTP request for debugging purposes
 * @param {functions.https.Request} request - The HTTP request object
 * @param {functions.Response} response - The HTTP response object
 */
export const fetchExchangeRatesManual = onRequest({
  memory: "256MiB",
  timeoutSeconds: 60,
}, async (request, response) => {
  logger.info("Manual exchange rate fetch triggered", {
    userAgent: request.get("User-Agent"),
    ip: request.ip,
  });

  try {
    const exchangeRateData = await fetchExchangeRateFromAPI();
    await storeExchangeRateInFirestore(exchangeRateData);

    response.status(200).json({
      success: true,
      data: {
        zarToUsdRate: exchangeRateData.zarToUsdRate,
        usdToZarRate: exchangeRateData.usdToZarRate,
        source: exchangeRateData.source,
        lastUpdated: exchangeRateData.lastUpdated.toDate().toISOString(),
      },
      message: "Exchange rates updated successfully",
    });
  } catch (error) {
    logger.error("Manual exchange rate fetch failed", {
      error: error instanceof Error ? error.message : String(error),
    });

    response.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch exchange rates",
    });
  }
});
